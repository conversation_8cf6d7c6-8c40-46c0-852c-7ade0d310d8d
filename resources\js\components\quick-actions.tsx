import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Icon } from '@/components/ui/icon';
import { BarChart3, Calendar, DollarSign, MessageSquare, Settings, UserPlus, Users } from 'lucide-react';

interface QuickAction {
    title: string;
    description: string;
    icon: any;
    href: string;
    variant: 'default' | 'outline';
    color: string;
}

const quickActions: QuickAction[] = [
    {
        title: 'Add New Member',
        description: 'Register a new church member',
        icon: UserPlus,
        href: '/members/new',
        variant: 'default',
        color: 'bg-primary hover:bg-primary/90',
    },
    {
        title: 'Schedule Event',
        description: 'Create a new church event',
        icon: Calendar,
        href: '/events/new',
        variant: 'outline',
        color: 'border-success text-success hover:bg-success/5',
    },
    {
        title: 'Record Donation',
        description: 'Log a new donation or tithe',
        icon: DollarSign,
        href: '/donations/new',
        variant: 'outline',
        color: 'border-info text-info hover:bg-info/5',
    },
    {
        title: 'Take Attendance',
        description: 'Record service attendance',
        icon: Users,
        href: '/attendance/new',
        variant: 'outline',
        color: 'border-warning text-warning hover:bg-warning/5',
    },
    {
        title: 'Send Message',
        description: 'Communicate with members',
        icon: MessageSquare,
        href: '/messages/new',
        variant: 'outline',
        color: 'border-purple text-purple hover:bg-purple-subtle',
    },
    {
        title: 'Generate Report',
        description: 'Create financial or attendance reports',
        icon: BarChart3,
        href: '/reports',
        variant: 'outline',
        color: 'border-teal text-teal hover:bg-teal-subtle',
    },
];

export function QuickActions() {
    const handleAction = (href: string) => {
        // For now, just log the action - in the real app this would navigate
        console.log('Navigate to:', href);
    };

    return (
        <Card className="shadow-sm transition-all duration-200 hover:shadow-md">
            <div className="p-5">
                <div className="mb-5 flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-foreground">Quick Actions</h3>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-muted/50">
                        <Icon iconNode={Settings} className="h-4 w-4" />
                    </Button>
                </div>

                <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                    {quickActions.map((action, index) => (
                        <Button
                            key={index}
                            variant={action.variant}
                            className={`h-auto p-4 text-left ${action.color} rounded border transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]`}
                            onClick={() => handleAction(action.href)}
                        >
                            <div className="flex w-full items-start gap-3">
                                <div className="mt-0.5 flex-shrink-0">
                                    <Icon iconNode={action.icon} className="h-5 w-5" />
                                </div>
                                <div className="min-w-0 flex-1 space-y-1">
                                    <div className="truncate text-sm leading-tight font-semibold sm:text-base">{action.title}</div>
                                    <div className="text-xs leading-relaxed break-words hyphens-auto opacity-75">{action.description}</div>
                                </div>
                            </div>
                        </Button>
                    ))}
                </div>
            </div>
        </Card>
    );
}
