<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('theme-showcase', function () {
        return Inertia::render('theme-showcase');
    })->name('theme-showcase');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
