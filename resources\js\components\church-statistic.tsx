import { Card } from '@/components/ui/card';
import { Icon } from '@/components/ui/icon';
import { type LucideIcon } from 'lucide-react';

interface ChurchStatisticProps {
    title: string;
    value: string | number;
    change?: {
        value: string;
        direction: 'up' | 'down' | 'neutral';
        period?: string;
    };
    icon: LucideIcon;
    variant: 'primary' | 'success' | 'info' | 'warning' | 'danger';
    description?: string;
}

const variantStyles = {
    primary: 'bg-white/20 text-white border-white/30',
    success: 'bg-white/20 text-white border-white/30',
    info: 'bg-white/20 text-white border-white/30',
    warning: 'bg-white/20 text-white border-white/30',
    danger: 'bg-white/20 text-white border-white/30',
};

const cardVariantStyles = {
    primary: 'bg-teal text-white border-teal',
    success: 'bg-green text-white border-green',
    info: 'bg-purple text-white border-purple',
    warning: 'bg-pink text-white border-pink',
    danger: 'bg-red text-white border-red',
};

const changeStyles = {
    up: 'text-white bg-white/20 border-white/30',
    down: 'text-white bg-white/20 border-white/30',
    neutral: 'text-white bg-white/20 border-white/30',
};

export function ChurchStatistic({ title, value, change, icon: IconComponent, variant, description }: ChurchStatisticProps) {
    return (
        <Card className={`relative overflow-hidden border ${cardVariantStyles[variant]} shadow-sm transition-all duration-200 hover:shadow-md`}>
            <div className="p-5">
                <div className="flex items-start justify-between">
                    <div className="min-w-0 flex-1">
                        <p className="mb-2 text-xs font-semibold tracking-widest text-white/80 uppercase">{title}</p>
                        <p className="mb-1 text-2xl font-bold text-white">{typeof value === 'number' ? value.toLocaleString() : value}</p>
                        {description && <p className="mb-3 text-sm leading-relaxed text-white/90">{description}</p>}
                        {change && (
                            <div className="flex items-center gap-2">
                                <span className={`rounded px-2 py-1 text-xs font-semibold ${changeStyles[change.direction]} border`}>
                                    {change.direction === 'up' && '↗'}
                                    {change.direction === 'down' && '↘'}
                                    {change.direction === 'neutral' && '→'}
                                    {change.value}
                                </span>
                                <span className="text-xs text-white/70">{change.period || 'vs last month'}</span>
                            </div>
                        )}
                    </div>
                    <div className={`ml-4 rounded p-3 ${variantStyles[variant]} shadow-sm`}>
                        <Icon iconNode={IconComponent} className="h-6 w-6" />
                    </div>
                </div>
            </div>
        </Card>
    );
}
