import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { BarChart3, Bell, CheckSquare, FileImage, FileText, LayoutGrid, Settings, Users, Users2 } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Admin Analytics',
        href: '/admin/analytics',
        icon: BarChart3,
    },
    {
        title: 'Live Attendance',
        href: '/live-attendance',
        icon: Users,
    },
    {
        title: 'Manual Checkin',
        href: '/manual-checkin',
        icon: CheckSquare,
    },
    {
        title: 'Reports',
        href: '/reports',
        icon: FileText,
    },
    {
        title: 'Members',
        href: '/members',
        icon: Users2,
    },
    {
        title: 'Contend Management',
        href: '/content-management',
        icon: FileImage,
    },
    {
        title: 'Notification',
        href: '/notifications',
        icon: Bell,
    },
    {
        title: 'Church Settings',
        href: '/checking-settings',
        icon: Settings,
    },
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="border-r-0">
            <SidebarHeader className="border-b border-sidebar-border/50 bg-sidebar">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild className="hover:bg-sidebar-accent/50">
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="bg-sidebar">
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border/50 bg-sidebar">
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
