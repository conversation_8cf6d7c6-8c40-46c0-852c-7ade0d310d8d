/**
 * Velonic Theme Showcase Page
 * Demonstrates all theme capabilities and components
 */

import { AppShell } from '@/components/app-shell';
import Heading from '@/components/heading';
import { ColorPalette, ThemeSettings, ThemeToggle } from '@/components/theme-settings';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardStats, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useTheme } from '@/hooks/use-theme';
import { Head } from '@inertiajs/react';

export default function ThemeShowcase() {
    const { config } = useTheme();

    return (
        <AppShell>
            <Head title="Theme Showcase" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <Heading title="Velonic Theme Showcase" description="Complete demonstration of the Velonic design system integration" />
                    </div>
                    <ThemeToggle showLabel />
                </div>

                <Separator />

                {/* Stats Cards */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <CardStats
                        title="Total Users"
                        value="12,345"
                        change="+12%"
                        changeType="positive"
                        icon={
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                                />
                            </svg>
                        }
                    />

                    <CardStats
                        title="Revenue"
                        value="$54,321"
                        change="+8.5%"
                        changeType="positive"
                        icon={
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                />
                            </svg>
                        }
                    />

                    <CardStats
                        title="Orders"
                        value="8,742"
                        change="-2.3%"
                        changeType="negative"
                        icon={
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                        }
                    />

                    <CardStats
                        title="Growth"
                        value="23.4%"
                        change="Steady"
                        changeType="neutral"
                        icon={
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        }
                    />
                </div>

                {/* Button Showcase */}
                <Card>
                    <CardHeader>
                        <CardTitle level={2}>Button Components</CardTitle>
                        <CardDescription>Complete button variants with Velonic color system</CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        {/* Primary Buttons */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Primary Variants</h4>
                            <div className="flex flex-wrap gap-3">
                                <Button color="blue">Blue</Button>
                                <Button color="indigo">Indigo</Button>
                                <Button color="purple">Purple</Button>
                                <Button color="pink">Pink</Button>
                                <Button color="red">Red</Button>
                                <Button color="orange">Orange</Button>
                                <Button color="yellow">Yellow</Button>
                                <Button color="green">Green</Button>
                                <Button color="teal">Teal</Button>
                                <Button color="cyan">Cyan</Button>
                            </div>
                        </div>

                        {/* Soft Buttons */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Soft Variants</h4>
                            <div className="flex flex-wrap gap-3">
                                <Button variant="soft" color="blue">
                                    Soft Blue
                                </Button>
                                <Button variant="soft" color="indigo">
                                    Soft Indigo
                                </Button>
                                <Button variant="soft" color="purple">
                                    Soft Purple
                                </Button>
                                <Button variant="soft" color="pink">
                                    Soft Pink
                                </Button>
                                <Button variant="soft" color="green">
                                    Soft Green
                                </Button>
                            </div>
                        </div>

                        {/* Outline Buttons */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Outline Variants</h4>
                            <div className="flex flex-wrap gap-3">
                                <Button variant="outline" color="blue">
                                    Outline Blue
                                </Button>
                                <Button variant="outline" color="indigo">
                                    Outline Indigo
                                </Button>
                                <Button variant="outline" color="purple">
                                    Outline Purple
                                </Button>
                                <Button variant="outline" color="pink">
                                    Outline Pink
                                </Button>
                                <Button variant="outline" color="green">
                                    Outline Green
                                </Button>
                            </div>
                        </div>

                        {/* Button Sizes */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Size Variants</h4>
                            <div className="flex items-center gap-3">
                                <Button size="sm" color="cyan">
                                    Small
                                </Button>
                                <Button size="default" color="cyan">
                                    Default
                                </Button>
                                <Button size="lg" color="cyan">
                                    Large
                                </Button>
                                <Button size="xl" color="cyan">
                                    Extra Large
                                </Button>
                            </div>
                        </div>

                        {/* Loading States */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Loading States</h4>
                            <div className="flex items-center gap-3">
                                <Button loading color="blue">
                                    Loading
                                </Button>
                                <Button loading variant="soft" color="indigo">
                                    Soft Loading
                                </Button>
                                <Button loading variant="outline" color="purple">
                                    Outline Loading
                                </Button>
                            </div>
                        </div>

                        {/* With Icons */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">With Icons</h4>
                            <div className="flex items-center gap-3">
                                <Button
                                    color="green"
                                    leftIcon={
                                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    }
                                >
                                    Add Item
                                </Button>
                                <Button
                                    color="blue"
                                    rightIcon={
                                        <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                        </svg>
                                    }
                                >
                                    Continue
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Badge Showcase */}
                <Card>
                    <CardHeader>
                        <CardTitle level={2}>Badge Components</CardTitle>
                        <CardDescription>Badge variants with complete color system</CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        {/* Standard Badges */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Standard Badges</h4>
                            <div className="flex flex-wrap gap-2">
                                <Badge color="blue">Blue</Badge>
                                <Badge color="indigo">Indigo</Badge>
                                <Badge color="purple">Purple</Badge>
                                <Badge color="pink">Pink</Badge>
                                <Badge color="red">Red</Badge>
                                <Badge color="orange">Orange</Badge>
                                <Badge color="yellow">Yellow</Badge>
                                <Badge color="green">Green</Badge>
                                <Badge color="teal">Teal</Badge>
                                <Badge color="cyan">Cyan</Badge>
                            </div>
                        </div>

                        {/* Soft Badges */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Soft Badges</h4>
                            <div className="flex flex-wrap gap-2">
                                <Badge variant="soft" color="blue">
                                    Soft Blue
                                </Badge>
                                <Badge variant="soft" color="indigo">
                                    Soft Indigo
                                </Badge>
                                <Badge variant="soft" color="purple">
                                    Soft Purple
                                </Badge>
                                <Badge variant="soft" color="pink">
                                    Soft Pink
                                </Badge>
                                <Badge variant="soft" color="green">
                                    Soft Green
                                </Badge>
                            </div>
                        </div>

                        {/* Outline Badges */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Outline Badges</h4>
                            <div className="flex flex-wrap gap-2">
                                <Badge variant="outline" color="blue">
                                    Outline Blue
                                </Badge>
                                <Badge variant="outline" color="indigo">
                                    Outline Indigo
                                </Badge>
                                <Badge variant="outline" color="purple">
                                    Outline Purple
                                </Badge>
                                <Badge variant="outline" color="pink">
                                    Outline Pink
                                </Badge>
                                <Badge variant="outline" color="green">
                                    Outline Green
                                </Badge>
                            </div>
                        </div>

                        {/* Badge Sizes */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Size Variants</h4>
                            <div className="flex items-center gap-2">
                                <Badge size="sm" color="cyan">
                                    Small
                                </Badge>
                                <Badge size="default" color="cyan">
                                    Default
                                </Badge>
                                <Badge size="lg" color="cyan">
                                    Large
                                </Badge>
                            </div>
                        </div>

                        {/* Special Features */}
                        <div className="space-y-3">
                            <h4 className="text-sm font-medium">Special Features</h4>
                            <div className="flex flex-wrap gap-2">
                                <Badge dot color="green">
                                    With Dot
                                </Badge>
                                <Badge removable color="blue" onRemove={() => console.log('Badge removed')}>
                                    Removable
                                </Badge>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Grid Layout */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Theme Settings */}
                    <div className="lg:col-span-1">
                        <ThemeSettings />
                    </div>

                    {/* Color Palette */}
                    <div className="lg:col-span-2">
                        <ColorPalette />
                    </div>
                </div>

                {/* Current Theme Info */}
                <Card>
                    <CardHeader>
                        <CardTitle level={2}>Current Theme Configuration</CardTitle>
                        <CardDescription>Live preview of your current theme settings</CardDescription>
                    </CardHeader>

                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-muted-foreground">Mode</h4>
                                <Badge variant="outline" className="capitalize">
                                    {config.mode}
                                </Badge>
                            </div>

                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-muted-foreground">Primary Color</h4>
                                <Badge color={config.primaryColor} className="capitalize">
                                    {config.primaryColor}
                                </Badge>
                            </div>

                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-muted-foreground">Sidebar Theme</h4>
                                <Badge variant="outline" className="capitalize">
                                    {config.sidebar}
                                </Badge>
                            </div>

                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-muted-foreground">Topbar Theme</h4>
                                <Badge variant="outline" className="capitalize">
                                    {config.topbar}
                                </Badge>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppShell>
    );
}
