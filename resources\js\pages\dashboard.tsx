import { ChurchStatistic } from '@/components/church-statistic';
import { RecentActivities } from '@/components/recent-activities';
import { SimpleChart } from '@/components/simple-chart';
import { UpcomingEvents } from '@/components/upcoming-events';
import { churchMetrics, recentActivities, upcomingEvents } from '@/data/church-dashboard-data';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Sample chart data
const attendanceData = [
    { name: 'Sunday Morning', value: 987, color: 'bg-primary' },
    { name: 'Wednesday Evening', value: 385, color: 'bg-info' },
    { name: 'Youth Service', value: 156, color: 'bg-success' },
    { name: 'Small Groups', value: 289, color: 'bg-warning' },
];

const donationData = [
    { name: 'Tithes', value: 24680, color: 'bg-success' },
    { name: 'Offerings', value: 4680, color: 'bg-info' },
    { name: 'Special Funds', value: 3200, color: 'bg-warning' },
    { name: 'Building Fund', value: 1850, color: 'bg-purple' },
];

export default function Dashboard() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Church Dashboard" />

            <div className="flex-1 space-y-6 p-6">
                {/* Welcome Section */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold tracking-tight">Welcome Back!</h1>
                    <p className="text-muted-foreground">Here's what's happening in your church community today.</p>
                </div>

                {/* Key Metrics Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {churchMetrics.map((metric, index) => (
                        <ChurchStatistic
                            key={index}
                            title={metric.title}
                            value={metric.value}
                            change={metric.change}
                            icon={metric.icon}
                            variant={metric.variant}
                            description={metric.description}
                        />
                    ))}
                </div>

                {/* Main Content Grid */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Left Column - Upcoming Events */}
                    <div className="lg:col-span-1">
                        <UpcomingEvents events={upcomingEvents} />
                    </div>

                    {/* Right Column - Recent Activities */}
                    <div className="lg:col-span-1">
                        <RecentActivities activities={recentActivities} />
                    </div>
                </div>

                {/* Chart Section */}
                <div className="grid gap-6 lg:grid-cols-2">
                    <SimpleChart title="Weekly Attendance Overview" data={attendanceData} />

                    <SimpleChart title="Monthly Donations ($)" data={donationData} />
                </div>
            </div>
        </AppLayout>
    );
}
