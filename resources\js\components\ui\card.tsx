import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "bg-card text-card-foreground flex flex-col rounded-xl border transition-[box-shadow,transform]",
  {
    variants: {
      variant: {
        default: "shadow-sm",
        elevated: "shadow-lg hover:shadow-xl",
        outlined: "border-2 shadow-none",
      },
      padding: {
        none: "",
        sm: "gap-4 py-4",
        default: "gap-6 py-6", 
        lg: "gap-8 py-8",
        xl: "gap-10 py-10",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "default",
    },
  }
)

interface CardProps extends 
  React.ComponentProps<"div">,
  VariantProps<typeof cardVariants> {
  loading?: boolean
}

function Card({ 
  className, 
  variant, 
  padding,
  loading,
  children,
  ...props 
}: CardProps) {
  return (
    <div
      data-slot="card"
      className={cn(
        cardVariants({ variant, padding }),
        loading && "relative overflow-hidden",
        className
      )}
      {...props}
    >
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-card/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      )}
      
      {/* Card content */}
      {children}
    </div>
  )
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn("flex flex-col gap-1.5 px-6", className)}
      {...props}
    />
  )
}

function CardTitle({ 
  className, 
  level = 3,
  ...props 
}: React.ComponentProps<"div"> & { level?: 1 | 2 | 3 | 4 | 5 | 6 }) {
  const headingClass = cn(
    "leading-none font-semibold",
    level === 1 && "text-2xl",
    level === 2 && "text-xl", 
    level === 3 && "text-lg",
    level === 4 && "text-base",
    level === 5 && "text-sm",
    level === 6 && "text-xs",
    className
  );
  
  switch (level) {
    case 1:
      return <h1 data-slot="card-title" className={headingClass} {...props} />;
    case 2:
      return <h2 data-slot="card-title" className={headingClass} {...props} />;
    case 3:
      return <h3 data-slot="card-title" className={headingClass} {...props} />;
    case 4:
      return <h4 data-slot="card-title" className={headingClass} {...props} />;
    case 5:
      return <h5 data-slot="card-title" className={headingClass} {...props} />;
    case 6:
      return <h6 data-slot="card-title" className={headingClass} {...props} />;
    default:
      return <h3 data-slot="card-title" className={headingClass} {...props} />;
  }
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6", className)}
      {...props}
    />
  )
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center px-6", className)}
      {...props}
    />
  )
}

// Velonic-specific card variants
function CardStats({ 
  className,
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  ...props
}: React.ComponentProps<"div"> & {
  title: string
  value: string | number
  change?: string | number
  changeType?: 'positive' | 'negative' | 'neutral'
  icon?: React.ReactNode
}) {
  return (
    <Card className={cn("card", className)} {...props}>
      <CardContent className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {change && (
            <p className={cn(
              "text-xs flex items-center gap-1",
              changeType === 'positive' && "text-green",
              changeType === 'negative' && "text-red",
              changeType === 'neutral' && "text-muted-foreground"
            )}>
              {changeType === 'positive' && '↗'}
              {changeType === 'negative' && '↘'}
              {change}
            </p>
          )}
        </div>
        {icon && (
          <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center text-primary">
            {icon}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export { 
  Card, 
  CardHeader, 
  CardFooter, 
  CardTitle, 
  CardDescription, 
  CardContent,
  CardStats,
  cardVariants,
  type CardProps 
}
