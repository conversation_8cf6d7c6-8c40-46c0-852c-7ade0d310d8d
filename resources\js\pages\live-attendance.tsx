import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { 
    Users, MapPin, Clock, Wifi, WifiOff, Eye, EyeOff, 
    Settings, RefreshCw, Download, Filter, Search,
    CheckCircle, XCircle, AlertCircle, TrendingUp
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Live Attendance',
        href: '/live-attendance',
    },
];

// Mock data for live attendance
interface AttendanceMember {
    id: number;
    name: string;
    avatar: string;
    status: 'present' | 'absent' | 'late';
    checkInTime?: string;
    checkOutTime?: string;
    location: 'main-sanctuary' | 'youth-hall' | 'children-area' | 'parking' | 'outside';
    department: string;
    isVolunteer: boolean;
}

const mockAttendanceData: AttendanceMember[] = [
    { id: 1, name: '<PERSON>', avatar: 'JS', status: 'present', checkInTime: '09:15 AM', location: 'main-sanctuary', department: 'Worship', isVolunteer: true },
    { id: 2, name: 'Sarah Johnson', avatar: 'SJ', status: 'present', checkInTime: '09:05 AM', location: 'youth-hall', department: 'Youth', isVolunteer: true },
    { id: 3, name: 'Michael Brown', avatar: 'MB', status: 'late', checkInTime: '09:45 AM', location: 'parking', department: 'Admin', isVolunteer: false },
    { id: 4, name: 'Emily Davis', avatar: 'ED', status: 'present', checkInTime: '08:55 AM', location: 'children-area', department: 'Children', isVolunteer: true },
    { id: 5, name: 'David Wilson', avatar: 'DW', status: 'absent', location: 'outside', department: 'Outreach', isVolunteer: false },
    { id: 6, name: 'Lisa Anderson', avatar: 'LA', status: 'present', checkInTime: '09:10 AM', location: 'main-sanctuary', department: 'Worship', isVolunteer: true },
    { id: 7, name: 'Robert Taylor', avatar: 'RT', status: 'present', checkInTime: '09:20 AM', location: 'main-sanctuary', department: 'Finance', isVolunteer: false },
    { id: 8, name: 'Jennifer Martinez', avatar: 'JM', status: 'present', checkInTime: '09:30 AM', location: 'youth-hall', department: 'Youth', isVolunteer: true },
];

const geofenceAreas = [
    { name: 'Main Sanctuary', count: 487, capacity: 600, status: 'active' },
    { name: 'Youth Hall', count: 89, capacity: 150, status: 'active' },
    { name: 'Children Area', count: 156, capacity: 200, status: 'active' },
    { name: 'Parking Lot', count: 23, capacity: 300, status: 'active' },
];

const statusColors = {
    present: 'bg-success text-success-foreground',
    absent: 'bg-muted text-muted-foreground',
    late: 'bg-warning text-warning-foreground',
};

const locationColors = {
    'main-sanctuary': 'bg-cyan text-white',
    'youth-hall': 'bg-blue text-white',
    'children-area': 'bg-purple text-white',
    'parking': 'bg-orange text-white',
    'outside': 'bg-muted text-muted-foreground',
};

export default function LiveAttendance() {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [isLive, setIsLive] = useState(true);
    const [selectedLocation, setSelectedLocation] = useState<string>('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Update current time every second
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    // Filter members based on location and search
    const filteredMembers = mockAttendanceData.filter(member => {
        const matchesLocation = selectedLocation === 'all' || member.location === selectedLocation;
        const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            member.department.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesLocation && matchesSearch;
    });

    const totalPresent = mockAttendanceData.filter(m => m.status === 'present').length;
    const totalLate = mockAttendanceData.filter(m => m.status === 'late').length;
    const totalAbsent = mockAttendanceData.filter(m => m.status === 'absent').length;
    const totalCapacity = geofenceAreas.reduce((sum, area) => sum + area.capacity, 0);
    const totalOccupancy = geofenceAreas.reduce((sum, area) => sum + area.count, 0);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Live Attendance" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <div className="flex items-center gap-3">
                            <h1 className="text-3xl font-bold tracking-tight">Live Attendance</h1>
                            <div className="flex items-center gap-2">
                                {isLive ? (
                                    <div className="flex items-center gap-2 text-success">
                                        <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
                                        <Wifi className="h-4 w-4" />
                                        <span className="text-sm font-medium">LIVE</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2 text-muted-foreground">
                                        <WifiOff className="h-4 w-4" />
                                        <span className="text-sm font-medium">OFFLINE</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        <p className="text-muted-foreground">
                            Real-time attendance monitoring • {currentTime.toLocaleTimeString()}
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4 mr-2" />
                            Geofence Settings
                        </Button>
                        <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export Data
                        </Button>
                        <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setIsLive(!isLive)}
                        >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            {isLive ? 'Pause' : 'Resume'}
                        </Button>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Total Present</p>
                                    <p className="text-2xl font-bold text-success">{totalPresent}</p>
                                    <div className="flex items-center gap-1">
                                        <TrendingUp className="h-4 w-4 text-success" />
                                        <span className="text-sm font-medium text-success">+12 from last week</span>
                                    </div>
                                </div>
                                <div className="p-3 rounded-lg bg-success text-white">
                                    <CheckCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Late Arrivals</p>
                                    <p className="text-2xl font-bold text-warning">{totalLate}</p>
                                    <p className="text-sm text-muted-foreground">Arrived after 9:30 AM</p>
                                </div>
                                <div className="p-3 rounded-lg bg-warning text-white">
                                    <AlertCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Absent</p>
                                    <p className="text-2xl font-bold text-muted-foreground">{totalAbsent}</p>
                                    <p className="text-sm text-muted-foreground">Expected but not present</p>
                                </div>
                                <div className="p-3 rounded-lg bg-muted text-muted-foreground">
                                    <XCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Capacity</p>
                                    <p className="text-2xl font-bold">{Math.round((totalOccupancy / totalCapacity) * 100)}%</p>
                                    <p className="text-sm text-muted-foreground">{totalOccupancy} / {totalCapacity}</p>
                                </div>
                                <div className="p-3 rounded-lg bg-cyan text-white">
                                    <Users className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Geofence Areas */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="mb-6">
                            <h3 className="text-lg font-semibold">Geofence Areas</h3>
                            <p className="text-sm text-muted-foreground">Real-time occupancy by location</p>
                        </div>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            {geofenceAreas.map((area, index) => (
                                <div key={index} className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <MapPin className="h-4 w-4 text-cyan" />
                                            <span className="font-medium">{area.name}</span>
                                        </div>
                                        <Badge variant="outline" className="text-xs">
                                            {area.status}
                                        </Badge>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>{area.count} people</span>
                                            <span className="text-muted-foreground">
                                                {Math.round((area.count / area.capacity) * 100)}%
                                            </span>
                                        </div>
                                        <div className="w-full bg-muted rounded-full h-2">
                                            <div 
                                                className="bg-cyan h-2 rounded-full transition-all duration-500" 
                                                style={{ width: `${(area.count / area.capacity) * 100}%` }}
                                            />
                                        </div>
                                        <p className="text-xs text-muted-foreground">
                                            Capacity: {area.capacity}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>

                {/* Live Member List */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h3 className="text-lg font-semibold">Live Member Status</h3>
                                <p className="text-sm text-muted-foreground">Real-time check-in/check-out tracking</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                    <input
                                        type="text"
                                        placeholder="Search members..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm"
                                    />
                                </div>
                                <select
                                    value={selectedLocation}
                                    onChange={(e) => setSelectedLocation(e.target.value)}
                                    className="px-3 py-2 border border-input rounded-md bg-background text-sm"
                                >
                                    <option value="all">All Locations</option>
                                    <option value="main-sanctuary">Main Sanctuary</option>
                                    <option value="youth-hall">Youth Hall</option>
                                    <option value="children-area">Children Area</option>
                                    <option value="parking">Parking</option>
                                    <option value="outside">Outside</option>
                                </select>
                            </div>
                        </div>

                        <div className="space-y-3">
                            {filteredMembers.map((member) => (
                                <div key={member.id} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                                    <div className="flex items-center gap-4">
                                        <div className="w-10 h-10 rounded-full bg-cyan text-white flex items-center justify-center font-medium">
                                            {member.avatar}
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium">{member.name}</span>
                                                {member.isVolunteer && (
                                                    <Badge variant="outline" className="text-xs">Volunteer</Badge>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground">{member.department}</p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-4">
                                        <div className="text-right">
                                            {member.checkInTime && (
                                                <div className="flex items-center gap-1 text-sm">
                                                    <Clock className="h-3 w-3" />
                                                    <span>{member.checkInTime}</span>
                                                </div>
                                            )}
                                            {member.checkOutTime && (
                                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                                    <span>Out: {member.checkOutTime}</span>
                                                </div>
                                            )}
                                        </div>
                                        
                                        <Badge className={locationColors[member.location]}>
                                            {member.location.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </Badge>
                                        
                                        <Badge className={statusColors[member.status]}>
                                            {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                                        </Badge>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>
            </div>
        </AppLayout>
    );
}
