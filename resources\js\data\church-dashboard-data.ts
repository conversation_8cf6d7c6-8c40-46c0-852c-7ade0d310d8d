import { 
    Users, 
    Calendar, 
    DollarSign, 
    TrendingUp, 
    UserPlus, 
    MapPin,
    Clock,
    Heart,
    BookOpen,
    Music
} from 'lucide-react';

export interface ChurchMetric {
    title: string;
    value: string | number;
    change?: {
        value: string;
        direction: 'up' | 'down' | 'neutral';
        period?: string;
    };
    icon: any;
    variant: 'primary' | 'success' | 'info' | 'warning' | 'danger';
    description?: string;
}

export interface UpcomingEvent {
    id: number;
    title: string;
    date: string;
    time: string;
    location: string;
    type: 'service' | 'meeting' | 'event' | 'study';
    attendees?: number;
}

export interface RecentActivity {
    id: number;
    type: 'member_joined' | 'donation' | 'attendance' | 'event_created';
    description: string;
    timestamp: string;
    user?: string;
    amount?: string;
}

// Mock data for church dashboard
export const churchMetrics: ChurchMetric[] = [
    {
        title: 'Total Members',
        value: 1248,
        change: {
            value: '+12.5%',
            direction: 'up',
            period: 'vs last month'
        },
        icon: Users,
        variant: 'primary',
        description: 'Active church members'
    },
    {
        title: 'Weekly Attendance',
        value: 987,
        change: {
            value: '+8.2%',
            direction: 'up',
            period: 'vs last week'
        },
        icon: TrendingUp,
        variant: 'success',
        description: 'Average weekly service attendance'
    },
    {
        title: 'Monthly Donations',
        value: '$24,680',
        change: {
            value: '+15.3%',
            direction: 'up',
            period: 'vs last month'
        },
        icon: DollarSign,
        variant: 'info',
        description: 'Total monthly contributions'
    },
    {
        title: 'Upcoming Events',
        value: 8,
        change: {
            value: '3 this week',
            direction: 'neutral',
            period: ''
        },
        icon: Calendar,
        variant: 'warning',
        description: 'Events scheduled this month'
    },
    {
        title: 'New Members',
        value: 23,
        change: {
            value: '+18.9%',
            direction: 'up',
            period: 'this month'
        },
        icon: UserPlus,
        variant: 'success',
        description: 'New members joined this month'
    },
    {
        title: 'Small Groups',
        value: 42,
        change: {
            value: '+2',
            direction: 'up',
            period: 'new groups'
        },
        icon: Heart,
        variant: 'primary',
        description: 'Active small groups'
    }
];

export const upcomingEvents: UpcomingEvent[] = [
    {
        id: 1,
        title: 'Sunday Morning Service',
        date: '2025-09-14',
        time: '10:00 AM',
        location: 'Main Sanctuary',
        type: 'service',
        attendees: 850
    },
    {
        id: 2,
        title: 'Youth Group Meeting',
        date: '2025-09-15',
        time: '6:00 PM',
        location: 'Youth Center',
        type: 'meeting',
        attendees: 45
    },
    {
        id: 3,
        title: 'Bible Study - Book of James',
        date: '2025-09-16',
        time: '7:00 PM',
        location: 'Fellowship Hall',
        type: 'study',
        attendees: 32
    },
    {
        id: 4,
        title: 'Community Outreach',
        date: '2025-09-18',
        time: '9:00 AM',
        location: 'Downtown Community Center',
        type: 'event',
        attendees: 120
    },
    {
        id: 5,
        title: 'Worship Team Practice',
        date: '2025-09-19',
        time: '7:30 PM',
        location: 'Main Sanctuary',
        type: 'meeting',
        attendees: 12
    }
];

export const recentActivities: RecentActivity[] = [
    {
        id: 1,
        type: 'member_joined',
        description: 'Sarah Johnson joined as a new member',
        timestamp: '2 hours ago',
        user: 'Sarah Johnson'
    },
    {
        id: 2,
        type: 'donation',
        description: 'Received online donation',
        timestamp: '4 hours ago',
        amount: '$250.00'
    },
    {
        id: 3,
        type: 'attendance',
        description: 'Wednesday evening service attendance recorded',
        timestamp: '1 day ago'
    },
    {
        id: 4,
        type: 'event_created',
        description: 'Created new event: Community Picnic',
        timestamp: '2 days ago',
        user: 'Pastor Mike'
    },
    {
        id: 5,
        type: 'donation',
        description: 'Received tithe donation',
        timestamp: '2 days ago',
        amount: '$180.00'
    }
];

// Chart data for attendance trends
export const attendanceChartData = {
    series: [
        {
            name: 'Sunday Morning',
            data: [820, 845, 867, 892, 901, 887, 932, 945, 967, 987, 1002, 994]
        },
        {
            name: 'Wednesday Evening',
            data: [245, 267, 289, 301, 287, 312, 298, 334, 356, 347, 372, 385]
        }
    ],
    categories: [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]
};

// Chart data for donation trends
export const donationChartData = {
    series: [
        {
            name: 'Tithes',
            data: [18500, 19200, 18800, 20100, 19600, 21200, 20800, 22100, 21900, 23400, 24100, 24680]
        },
        {
            name: 'Offerings',
            data: [3200, 3400, 3100, 3600, 3800, 3500, 4100, 3900, 4200, 4500, 4300, 4680]
        },
        {
            name: 'Special Funds',
            data: [1200, 1800, 2100, 1600, 2300, 1900, 2500, 2800, 2400, 3100, 2900, 3200]
        }
    ],
    categories: [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]
};
