@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    /* === FONT SYSTEM === */
    --font-sans: 'Lato', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    
    /* === VELONIC BRAND COLORS === */
    --color-blue: #4489e4;
    --color-indigo: #33b0e0;
    --color-purple: #716cb0;
    --color-pink: #f24f7c;
    --color-red: #d03f3f;
    --color-orange: #f7931e;
    --color-yellow: #f9c851;
    --color-green: #22c55e;
    --color-teal: #3cc0c3;
    --color-cyan: #34b0e0;

    /* === EXTENDED GRAYSCALE SYSTEM === */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    --color-gray-950: #030712;

    /* === BORDER RADIUS SYSTEM === */
    --radius-sm: 0.25rem;   /* 4px */
    --radius: 0.375rem;     /* 6px */
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: 0.5rem;    /* 8px */
    --radius-xl: 1rem;      /* 16px */
    --radius-xxl: 2rem;     /* 32px */

    /* === SEMANTIC COLOR MAPPINGS === */
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    
    /* Primary uses Velonic cyan */
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    
    /* Secondary uses gray-600 */
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    
    /* Success uses Velonic blue */
    --color-success: var(--success);
    --color-success-foreground: var(--success-foreground);
    
    /* Info uses Velonic indigo */
    --color-info: var(--info);
    --color-info-foreground: var(--info-foreground);
    
    /* Warning uses Velonic yellow */
    --color-warning: var(--warning);
    --color-warning-foreground: var(--warning-foreground);
    
    /* Danger uses Velonic red */
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    /* === LAYOUT DIMENSIONS === */
    --sidebar-width: 240px;
    --sidebar-width-md: 160px;
    --sidebar-width-sm: 70px;
    --topbar-height: 70px;
    --logo-height: 24px;

    /* === SIDEBAR THEMING === */
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    /* === MENU THEMING === */
    --color-menu-bg: var(--menu-bg);
    --color-menu-item: var(--menu-item-color);
    --color-menu-item-hover: var(--menu-item-hover-color);
    --color-menu-item-active: var(--menu-item-active-color);
    --color-menu-item-active-bg: var(--menu-item-active-bg);

    /* === TOPBAR THEMING === */
    --color-topbar-bg: var(--topbar-bg);
    --color-topbar-item: var(--topbar-item-color);
    --color-topbar-item-hover: var(--topbar-item-hover-color);
    --color-topbar-search-bg: var(--topbar-search-bg);

    /* === CHART COLORS === */
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
}

/* === LIGHT THEME (ROOT) === */
:root {
    /* Core Background & Foreground */
    --background: white;
    --foreground: var(--color-gray-900);
    
    /* Card System */
    --card: white;
    --card-foreground: var(--color-gray-900);
    
    /* Popover System */
    --popover: white;
    --popover-foreground: var(--color-gray-900);
    
    /* Primary Theme - Velonic Cyan */
    --primary: var(--color-cyan);
    --primary-foreground: white;
    
    /* Secondary Theme - Gray 600 */
    --secondary: var(--color-gray-100);
    --secondary-foreground: var(--color-gray-900);
    
    /* Success Theme - Velonic Blue */
    --success: var(--color-blue);
    --success-foreground: white;
    
    /* Info Theme - Velonic Indigo */
    --info: var(--color-indigo);
    --info-foreground: white;
    
    /* Warning Theme - Velonic Yellow */
    --warning: var(--color-yellow);
    --warning-foreground: var(--color-gray-900);
    
    /* Destructive Theme - Velonic Red */
    --destructive: var(--color-red);
    --destructive-foreground: white;
    
    /* Muted System */
    --muted: var(--color-gray-100);
    --muted-foreground: var(--color-gray-500);
    
    /* Accent System */
    --accent: var(--color-gray-100);
    --accent-foreground: var(--color-gray-900);
    
    /* Border & Input System */
    --border: var(--color-gray-200);
    --input: var(--color-gray-200);
    --ring: var(--color-gray-300);
    
    /* Chart Colors */
    --chart-1: var(--color-blue);
    --chart-2: var(--color-indigo);
    --chart-3: var(--color-purple);
    --chart-4: var(--color-green);
    --chart-5: var(--color-orange);
    
    /* Sidebar Light Theme */
    --sidebar: white;
    --sidebar-foreground: var(--color-gray-700);
    --sidebar-primary: var(--color-cyan);
    --sidebar-primary-foreground: white;
    --sidebar-accent: var(--color-gray-100);
    --sidebar-accent-foreground: var(--color-gray-900);
    --sidebar-border: var(--color-gray-200);
    --sidebar-ring: var(--color-gray-300);
    
    /* Menu Light Theme */
    --menu-bg: white;
    --menu-item-color: var(--color-gray-700);
    --menu-item-hover-color: var(--color-gray-900);
    --menu-item-active-color: var(--color-cyan);
    --menu-item-active-bg: color-mix(in srgb, var(--color-cyan) 15%, transparent);
    
    /* Topbar Light Theme */
    --topbar-bg: white;
    --topbar-item-color: var(--color-gray-700);
    --topbar-item-hover-color: var(--color-gray-900);
    --topbar-search-bg: var(--color-gray-100);
}

/* === DARK THEME === */
.dark {
    /* Core Background & Foreground */
    --background: var(--color-gray-900);
    --foreground: var(--color-gray-100);
    
    /* Card System */
    --card: var(--color-gray-800);
    --card-foreground: var(--color-gray-100);
    
    /* Popover System */
    --popover: var(--color-gray-800);
    --popover-foreground: var(--color-gray-100);
    
    /* Primary Theme - Brighter in dark mode */
    --primary: var(--color-cyan);
    --primary-foreground: var(--color-gray-900);
    
    /* Secondary Theme */
    --secondary: var(--color-gray-700);
    --secondary-foreground: var(--color-gray-100);
    
    /* Success Theme */
    --success: var(--color-blue);
    --success-foreground: white;
    
    /* Info Theme */
    --info: var(--color-indigo);
    --info-foreground: white;
    
    /* Warning Theme */
    --warning: var(--color-yellow);
    --warning-foreground: var(--color-gray-900);
    
    /* Destructive Theme */
    --destructive: var(--color-red);
    --destructive-foreground: white;
    
    /* Muted System */
    --muted: var(--color-gray-700);
    --muted-foreground: var(--color-gray-400);
    
    /* Accent System */
    --accent: var(--color-gray-700);
    --accent-foreground: var(--color-gray-100);
    
    /* Border & Input System */
    --border: var(--color-gray-700);
    --input: var(--color-gray-700);
    --ring: var(--color-gray-600);
    
    /* Chart Colors - Adjusted for dark mode */
    --chart-1: var(--color-blue);
    --chart-2: var(--color-indigo);
    --chart-3: var(--color-purple);
    --chart-4: var(--color-green);
    --chart-5: var(--color-orange);
    
    /* Sidebar Dark Theme */
    --sidebar: #1a2942;
    --sidebar-foreground: #70809a;
    --sidebar-primary: var(--color-cyan);
    --sidebar-primary-foreground: white;
    --sidebar-accent: var(--color-gray-700);
    --sidebar-accent-foreground: var(--color-gray-100);
    --sidebar-border: var(--color-gray-700);
    --sidebar-ring: var(--color-gray-600);
    
    /* Menu Dark Theme */
    --menu-bg: #1a2942;
    --menu-item-color: #70809a;
    --menu-item-hover-color: var(--color-gray-100);
    --menu-item-active-color: var(--color-cyan);
    --menu-item-active-bg: color-mix(in srgb, var(--color-cyan) 15%, transparent);
    
    /* Topbar Dark Theme */
    --topbar-bg: #1a2942;
    --topbar-item-color: #70809a;
    --topbar-item-hover-color: var(--color-gray-100);
    --topbar-search-bg: var(--color-gray-700);
}

/* === BASE STYLES === */
@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground font-sans;
        font-feature-settings: "rlig" 1, "calt" 1;
    }
    
    /* Typography Scale */
    .text-xs { font-size: 0.75rem; }      /* 12px */
    .text-sm { font-size: 0.875rem; }     /* 14px */
    .text-base { font-size: 1rem; }       /* 16px */
    .text-lg { font-size: 1.125rem; }     /* 18px */
    .text-xl { font-size: 1.25rem; }      /* 20px */
    .text-2xl { font-size: 1.5rem; }      /* 24px */
    
    /* Custom Font Sizes from Velonic */
    .text-10 { font-size: 0.625rem; }     /* 10px */
    .text-11 { font-size: 0.6875rem; }    /* 11px */
    .text-13 { font-size: 0.8125rem; }    /* 13px */
    .text-15 { font-size: 0.9375rem; }    /* 15px */
    
    /* Font Weights */
    .font-light { font-weight: 300; }
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }
    .font-black { font-weight: 900; }
}

/* === COMPONENT STYLES === */
@layer components {
    /* Card System with Velonic Shadow */
    .card {
        @apply bg-card text-card-foreground rounded-lg border border-border;
        box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);
    }
    
    .card-header {
        @apply flex flex-col space-y-1.5 p-6;
    }
    
    .card-body {
        @apply p-6 pt-0;
    }
    
    .card-footer {
        @apply flex items-center p-6 pt-0;
    }
    
    /* Button Soft Variants */
    .btn-soft-primary {
        @apply bg-primary/10 text-primary hover:bg-primary/20 border-primary/20;
    }
    
    .btn-soft-secondary {
        @apply bg-secondary/10 text-secondary-foreground hover:bg-secondary/20 border-secondary/20;
    }
    
    .btn-soft-success {
        @apply text-success border-success/20;
        background-color: color-mix(in srgb, var(--success) 10%, transparent);
    }
    
    .btn-soft-info {
        @apply text-info border-info/20;
        background-color: color-mix(in srgb, var(--info) 10%, transparent);
    }
    
    .btn-soft-warning {
        @apply text-warning border-warning/20;
        background-color: color-mix(in srgb, var(--warning) 10%, transparent);
    }
    
    .btn-soft-danger {
        @apply bg-destructive/10 text-destructive hover:bg-destructive/20 border-destructive/20;
    }
    
    /* Badge Outline Variants */
    .badge-outline-primary {
        @apply border-2 border-primary text-primary bg-transparent hover:bg-primary/5;
    }
    
    .badge-outline-secondary {
        @apply border-2 border-secondary text-secondary-foreground bg-transparent hover:bg-secondary/5;
    }
    
    .badge-outline-success {
        @apply border-2 text-success bg-transparent hover:bg-success/5;
        border-color: var(--success);
    }
    
    .badge-outline-danger {
        @apply border-2 border-destructive text-destructive bg-transparent hover:bg-destructive/5;
    }
    
    /* Form Control Light */
    .form-control-light {
        @apply bg-muted/50 border-border rounded-md px-3 py-2 text-sm;
        @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    }
    
    /* Sidebar Menu Items */
    .menu-item {
        @apply flex items-center gap-3 rounded-md px-6 py-2.5 text-sm font-medium;
        @apply text-menu-item hover:text-menu-item-hover hover:bg-menu-item-active-bg/50;
        @apply transition-colors duration-200;
    }
    
    .menu-item.active {
        @apply text-menu-item-active bg-menu-item-active-bg;
    }
    
    .menu-item-icon {
        @apply w-5 h-5 shrink-0;
    }
    
    /* Topbar Styles */
    .topbar {
        @apply bg-topbar-bg border-b border-border;
        height: var(--topbar-height);
    }
    
    .topbar-item {
        @apply text-topbar-item hover:text-topbar-item-hover;
    }
    
    /* Utility Classes */
    .border-dashed {
        border-style: dashed !important;
    }
    
    /* Velonic Extended Colors */
    .bg-pink-subtle { background-color: color-mix(in srgb, var(--color-pink) 10%, transparent); }
    .bg-purple-subtle { background-color: color-mix(in srgb, var(--color-purple) 10%, transparent); }
    .bg-blue-subtle { background-color: color-mix(in srgb, var(--color-blue) 10%, transparent); }
    .bg-indigo-subtle { background-color: color-mix(in srgb, var(--color-indigo) 10%, transparent); }
    .bg-green-subtle { background-color: color-mix(in srgb, var(--color-green) 10%, transparent); }
    .bg-orange-subtle { background-color: color-mix(in srgb, var(--color-orange) 10%, transparent); }
    .bg-yellow-subtle { background-color: color-mix(in srgb, var(--color-yellow) 10%, transparent); }
    .bg-teal-subtle { background-color: color-mix(in srgb, var(--color-teal) 10%, transparent); }
    
    .text-pink { color: var(--color-pink); }
    .text-purple { color: var(--color-purple); }
    .text-blue { color: var(--color-blue); }
    .text-indigo { color: var(--color-indigo); }
    .text-green { color: var(--color-green); }
    .text-orange { color: var(--color-orange); }
    .text-yellow { color: var(--color-yellow); }
    .text-teal { color: var(--color-teal); }
    
    /* Enhanced Sidebar Styling for Velonic Design */
    [data-sidebar="sidebar"] {
        background: var(--sidebar);
        border-right: 1px solid var(--sidebar-border);
    }
    
    /* Sidebar group label styling to match Velonic */
    [data-sidebar="group-label"] {
        color: var(--sidebar-foreground);
        opacity: 0.6;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        margin-bottom: 0.5rem;
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Enhanced menu button styling */
    [data-sidebar="menu-button"] {
        color: var(--menu-item-color);
        transition: all 0.2s ease-in-out;
        border-radius: 0.5rem;
        font-weight: 500;
    }
    
    [data-sidebar="menu-button"]:hover {
        background-color: var(--menu-item-active-bg);
        color: var(--menu-item-hover-color);
        transform: translateX(2px);
    }
    
    [data-sidebar="menu-button"][data-active="true"] {
        background-color: var(--menu-item-active-bg);
        color: var(--menu-item-active-color);
        font-weight: 600;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    }
    
    /* Icon styling for menu items */
    [data-sidebar="menu-button"] svg {
        width: 1.25rem;
        height: 1.25rem;
        transition: color 0.2s ease-in-out;
    }
    
    /* Sidebar footer styling */
    [data-sidebar="footer"] {
        border-top: 1px solid var(--sidebar-border);
        background: var(--sidebar);
    }
    
    /* Sidebar header styling */
    [data-sidebar="header"] {
        border-bottom: 1px solid var(--sidebar-border);
        background: var(--sidebar);
    }
    
    /* Enhanced topbar styling for Velonic design */
    header[class*="sticky"] {
        backdrop-filter: blur(8px);
        background: var(--topbar-bg);
        border-bottom: 1px solid var(--border);
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    }
    
    /* Topbar search input styling */
    header input[type="search"] {
        background: var(--topbar-search-bg);
        border: 1px solid var(--border);
        transition: all 0.2s ease-in-out;
    }
    
    header input[type="search"]:focus {
        background: var(--background);
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgb(var(--primary) / 0.1);
    }
    
    /* Topbar button styling */
    header button[data-state] {
        transition: all 0.2s ease-in-out;
    }
    
    header button[data-state]:hover {
        background: var(--sidebar-accent);
        color: var(--sidebar-accent-foreground);
        transform: translateY(-1px);
    }
    
    /* Notification badge styling */
    .notification-badge {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: .8;
        }
    }
    
    /* Dropdown menu enhancements */
    [data-radix-popper-content-wrapper] {
        filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
    }
    
    /* Custom Velonic color classes */
    .text-purple { color: var(--color-purple); }
    .border-purple { border-color: var(--color-purple); }
    .bg-purple { background-color: var(--color-purple); }
    
    .text-pink { color: var(--color-pink); }
    .border-pink { border-color: var(--color-pink); }
    .bg-pink { background-color: var(--color-pink); }
    
    .text-teal { color: var(--color-teal); }
    .border-teal { border-color: var(--color-teal); }
    .bg-teal { background-color: var(--color-teal); }
    
    .text-cyan { color: var(--color-cyan); }
    .border-cyan { border-color: var(--color-cyan); }
    .bg-cyan { background-color: var(--color-cyan); }
}
