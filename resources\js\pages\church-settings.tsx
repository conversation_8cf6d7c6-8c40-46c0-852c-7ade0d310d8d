import { ChurchMapComponent } from '@/components/church-map';
import { Alert } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { AlertTriangle, Database, MapPin, Settings, Smartphone, Users } from 'lucide-react';
import { useState } from 'react';

interface ChurchSettings {
    id?: number;
    church_name?: string;
    church_address?: string;
    church_phone?: string;
    church_email?: string;
    church_website?: string;
    social_media_links?: {
        facebook?: string;
        twitter?: string;
        instagram?: string;
        youtube?: string;
    };
    church_description?: string;
    mission_statement?: string;
    service_times?: Array<{
        day: string;
        time: string;
        service_type: string;
    }>;
    leadership_info?: Array<{
        name: string;
        position: string;
        bio?: string;
    }>;
    latitude?: number;
    longitude?: number;
    geofence_radius?: number;
    attendance_preferences?: {
        auto_checkin?: boolean;
        notification_enabled?: boolean;
        late_threshold?: number;
    };
    manual_checkin_settings?: {
        enabled?: boolean;
        admin_only?: boolean;
        time_window?: number;
    };
    notification_settings?: {
        email_enabled?: boolean;
        sms_enabled?: boolean;
        push_enabled?: boolean;
    };
    mobile_app_config?: {
        app_name?: string;
        theme_color?: string;
        notification_enabled?: boolean;
    };
}

interface ChurchSettingsPageProps {
    settings: ChurchSettings;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Church Settings', href: '/church-settings' },
];

export default function ChurchSettingsPage({ settings }: ChurchSettingsPageProps) {
    const [formData, setFormData] = useState<ChurchSettings>({
        church_name: settings?.church_name || '',
        church_address: settings?.church_address || '',
        church_phone: settings?.church_phone || '',
        church_email: settings?.church_email || '',
        church_website: settings?.church_website || '',
        social_media_links: settings?.social_media_links || {},
        church_description: settings?.church_description || '',
        mission_statement: settings?.mission_statement || '',
        service_times: settings?.service_times || [{ day: '', time: '', service_type: '' }],
        leadership_info: settings?.leadership_info || [{ name: '', position: '', bio: '' }],
        latitude: settings?.latitude || 0,
        longitude: settings?.longitude || 0,
        geofence_radius: settings?.geofence_radius || 100,
        attendance_preferences: settings?.attendance_preferences || {
            auto_checkin: true,
            notification_enabled: true,
            late_threshold: 30,
        },
        manual_checkin_settings: settings?.manual_checkin_settings || {
            enabled: true,
            admin_only: false,
            time_window: 120,
        },
        notification_settings: settings?.notification_settings || {
            email_enabled: true,
            sms_enabled: false,
            push_enabled: true,
        },
        mobile_app_config: settings?.mobile_app_config || {
            app_name: 'Church App',
            theme_color: '#34B0E0',
            notification_enabled: true,
        },
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);

    const handleInputChange = (field: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleNestedChange = (parent: string, field: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [parent]: {
                ...prev[parent as keyof ChurchSettings],
                [field]: value,
            },
        }));
    };

    const handleServiceTimeChange = (index: number, field: string, value: string) => {
        const newServiceTimes = [...(formData.service_times || [])];
        newServiceTimes[index] = { ...newServiceTimes[index], [field]: value };
        setFormData((prev) => ({
            ...prev,
            service_times: newServiceTimes,
        }));
    };

    const addServiceTime = () => {
        setFormData((prev) => ({
            ...prev,
            service_times: [...(prev.service_times || []), { day: '', time: '', service_type: '' }],
        }));
    };

    const removeServiceTime = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            service_times: prev.service_times?.filter((_, i) => i !== index) || [],
        }));
    };

    const handleLeadershipChange = (index: number, field: string, value: string) => {
        const newLeadership = [...(formData.leadership_info || [])];
        newLeadership[index] = { ...newLeadership[index], [field]: value };
        setFormData((prev) => ({
            ...prev,
            leadership_info: newLeadership,
        }));
    };

    const addLeadership = () => {
        setFormData((prev) => ({
            ...prev,
            leadership_info: [...(prev.leadership_info || []), { name: '', position: '', bio: '' }],
        }));
    };

    const removeLeadership = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            leadership_info: prev.leadership_info?.filter((_, i) => i !== index) || [],
        }));
    };

    const handleLocationUpdate = (lat: number, lng: number) => {
        setFormData((prev) => ({
            ...prev,
            latitude: lat,
            longitude: lng,
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setShowConfirmDialog(true);
    };

    const confirmSubmit = () => {
        setIsSubmitting(true);
        router.put('/church-settings', formData, {
            onSuccess: () => {
                setIsSubmitting(false);
                setShowConfirmDialog(false);
            },
            onError: () => {
                setIsSubmitting(false);
                setShowConfirmDialog(false);
            },
        });
    };

    const testGeofence = () => {
        if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const distance = calculateDistance(
                        position.coords.latitude,
                        position.coords.longitude,
                        formData.latitude || 0,
                        formData.longitude || 0,
                    );

                    const isWithinGeofence = distance <= (formData.geofence_radius || 100);
                    alert(`You are ${distance.toFixed(0)}m from the church. ${isWithinGeofence ? 'Within' : 'Outside'} geofence.`);
                },
                (error) => {
                    alert('Unable to get your location: ' + error.message);
                },
            );
        } else {
            alert('Geolocation is not supported by this browser.');
        }
    };

    const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
        const R = 6371e3; // Earth's radius in meters
        const φ1 = (lat1 * Math.PI) / 180;
        const φ2 = (lat2 * Math.PI) / 180;
        const Δφ = ((lat2 - lat1) * Math.PI) / 180;
        const Δλ = ((lon2 - lon1) * Math.PI) / 180;

        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Church Settings" />

            <div className="flex-1 space-y-6 p-6">
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold tracking-tight">Church Settings</h1>
                    <p className="text-muted-foreground">Configure your church information, location, and system preferences.</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Church Information Section */}
                    <Card className="p-6">
                        <div className="mb-6 flex items-center gap-2">
                            <Settings className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Church Information</h2>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor="church_name">Church Name *</Label>
                                <Input
                                    id="church_name"
                                    value={formData.church_name}
                                    onChange={(e) => handleInputChange('church_name', e.target.value)}
                                    placeholder="Enter church name"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="church_email">Church Email</Label>
                                <Input
                                    id="church_email"
                                    type="email"
                                    value={formData.church_email}
                                    onChange={(e) => handleInputChange('church_email', e.target.value)}
                                    placeholder="<EMAIL>"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="church_phone">Church Phone</Label>
                                <Input
                                    id="church_phone"
                                    value={formData.church_phone}
                                    onChange={(e) => handleInputChange('church_phone', e.target.value)}
                                    placeholder="+****************"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="church_website">Website URL</Label>
                                <Input
                                    id="church_website"
                                    type="url"
                                    value={formData.church_website}
                                    onChange={(e) => handleInputChange('church_website', e.target.value)}
                                    placeholder="https://www.yourchurch.com"
                                />
                            </div>

                            <div className="space-y-2 md:col-span-2">
                                <Label htmlFor="church_address">Church Address</Label>
                                <Textarea
                                    id="church_address"
                                    value={formData.church_address}
                                    onChange={(e) => handleInputChange('church_address', e.target.value)}
                                    placeholder="Enter complete church address"
                                    rows={3}
                                />
                            </div>

                            <div className="space-y-2 md:col-span-2">
                                <Label htmlFor="church_description">Church Description</Label>
                                <Textarea
                                    id="church_description"
                                    value={formData.church_description}
                                    onChange={(e) => handleInputChange('church_description', e.target.value)}
                                    placeholder="Brief description of your church"
                                    rows={3}
                                />
                            </div>

                            <div className="space-y-2 md:col-span-2">
                                <Label htmlFor="mission_statement">Mission Statement</Label>
                                <Textarea
                                    id="mission_statement"
                                    value={formData.mission_statement}
                                    onChange={(e) => handleInputChange('mission_statement', e.target.value)}
                                    placeholder="Your church's mission statement"
                                    rows={4}
                                />
                            </div>
                        </div>

                        <Separator className="my-6" />

                        {/* Social Media Links */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Social Media Links</h3>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="facebook">Facebook URL</Label>
                                    <Input
                                        id="facebook"
                                        type="url"
                                        value={formData.social_media_links?.facebook || ''}
                                        onChange={(e) => handleNestedChange('social_media_links', 'facebook', e.target.value)}
                                        placeholder="https://facebook.com/yourchurch"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="twitter">Twitter/X URL</Label>
                                    <Input
                                        id="twitter"
                                        type="url"
                                        value={formData.social_media_links?.twitter || ''}
                                        onChange={(e) => handleNestedChange('social_media_links', 'twitter', e.target.value)}
                                        placeholder="https://twitter.com/yourchurch"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="instagram">Instagram URL</Label>
                                    <Input
                                        id="instagram"
                                        type="url"
                                        value={formData.social_media_links?.instagram || ''}
                                        onChange={(e) => handleNestedChange('social_media_links', 'instagram', e.target.value)}
                                        placeholder="https://instagram.com/yourchurch"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="youtube">YouTube URL</Label>
                                    <Input
                                        id="youtube"
                                        type="url"
                                        value={formData.social_media_links?.youtube || ''}
                                        onChange={(e) => handleNestedChange('social_media_links', 'youtube', e.target.value)}
                                        placeholder="https://youtube.com/yourchurch"
                                    />
                                </div>
                            </div>
                        </div>

                        <Separator className="my-6" />

                        {/* Service Times */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Service Times</h3>
                                <Button type="button" variant="outline" onClick={addServiceTime}>
                                    Add Service
                                </Button>
                            </div>

                            {formData.service_times?.map((service, index) => (
                                <div key={index} className="grid items-end gap-4 md:grid-cols-4">
                                    <div className="space-y-2">
                                        <Label htmlFor={`service_day_${index}`}>Day</Label>
                                        <Input
                                            id={`service_day_${index}`}
                                            value={service.day}
                                            onChange={(e) => handleServiceTimeChange(index, 'day', e.target.value)}
                                            placeholder="Sunday"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor={`service_time_${index}`}>Time</Label>
                                        <Input
                                            id={`service_time_${index}`}
                                            value={service.time}
                                            onChange={(e) => handleServiceTimeChange(index, 'time', e.target.value)}
                                            placeholder="9:00 AM"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor={`service_type_${index}`}>Service Type</Label>
                                        <Input
                                            id={`service_type_${index}`}
                                            value={service.service_type}
                                            onChange={(e) => handleServiceTimeChange(index, 'service_type', e.target.value)}
                                            placeholder="Main Service"
                                        />
                                    </div>

                                    <div>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => removeServiceTime(index)}
                                            disabled={formData.service_times?.length === 1}
                                        >
                                            Remove
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <Separator className="my-6" />

                        {/* Leadership Information */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Leadership Information</h3>
                                <Button type="button" variant="outline" onClick={addLeadership}>
                                    Add Leader
                                </Button>
                            </div>

                            {formData.leadership_info?.map((leader, index) => (
                                <div key={index} className="space-y-4 rounded-lg border p-4">
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor={`leader_name_${index}`}>Name</Label>
                                            <Input
                                                id={`leader_name_${index}`}
                                                value={leader.name}
                                                onChange={(e) => handleLeadershipChange(index, 'name', e.target.value)}
                                                placeholder="Pastor John Doe"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor={`leader_position_${index}`}>Position</Label>
                                            <Input
                                                id={`leader_position_${index}`}
                                                value={leader.position}
                                                onChange={(e) => handleLeadershipChange(index, 'position', e.target.value)}
                                                placeholder="Senior Pastor"
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor={`leader_bio_${index}`}>Bio</Label>
                                        <Textarea
                                            id={`leader_bio_${index}`}
                                            value={leader.bio || ''}
                                            onChange={(e) => handleLeadershipChange(index, 'bio', e.target.value)}
                                            placeholder="Brief biography"
                                            rows={3}
                                        />
                                    </div>

                                    <div className="flex justify-end">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => removeLeadership(index)}
                                            disabled={formData.leadership_info?.length === 1}
                                        >
                                            Remove Leader
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </Card>

                    {/* Geolocation & Geofence Section */}
                    <Card className="p-6">
                        <div className="mb-6 flex items-center gap-2">
                            <MapPin className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Geolocation & Geofence Settings</h2>
                        </div>

                        <div className="space-y-6">
                            <div className="grid gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <Label htmlFor="latitude">Latitude</Label>
                                    <Input
                                        id="latitude"
                                        type="number"
                                        step="any"
                                        value={formData.latitude}
                                        onChange={(e) => handleInputChange('latitude', parseFloat(e.target.value))}
                                        placeholder="0.0000000"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="longitude">Longitude</Label>
                                    <Input
                                        id="longitude"
                                        type="number"
                                        step="any"
                                        value={formData.longitude}
                                        onChange={(e) => handleInputChange('longitude', parseFloat(e.target.value))}
                                        placeholder="0.0000000"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="geofence_radius">Geofence Radius (meters)</Label>
                                    <Input
                                        id="geofence_radius"
                                        type="number"
                                        min="10"
                                        max="10000"
                                        value={formData.geofence_radius}
                                        onChange={(e) => handleInputChange('geofence_radius', parseInt(e.target.value))}
                                        placeholder="100"
                                    />
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium">Interactive Map</h3>
                                    <Button type="button" variant="outline" onClick={testGeofence}>
                                        Test Geofence
                                    </Button>
                                </div>

                                <div className="h-96 overflow-hidden rounded-lg border">
                                    <ChurchMapComponent
                                        latitude={formData.latitude || 0}
                                        longitude={formData.longitude || 0}
                                        geofenceRadius={formData.geofence_radius || 100}
                                        onLocationUpdate={handleLocationUpdate}
                                    />
                                </div>

                                <Alert>
                                    <AlertTriangle className="h-4 w-4" />
                                    <div>
                                        <p className="text-sm">
                                            Click on the map to set the church location. The blue circle represents the geofence area.
                                        </p>
                                    </div>
                                </Alert>
                            </div>
                        </div>
                    </Card>

                    {/* Attendance Settings Section */}
                    <Card className="p-6">
                        <div className="mb-6 flex items-center gap-2">
                            <Users className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Attendance Settings</h2>
                        </div>

                        <div className="space-y-6">
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Attendance Preferences</h3>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="auto_checkin"
                                            checked={formData.attendance_preferences?.auto_checkin || false}
                                            onChange={(e) => handleNestedChange('attendance_preferences', 'auto_checkin', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="auto_checkin">Enable Auto Check-in</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="attendance_notifications"
                                            checked={formData.attendance_preferences?.notification_enabled || false}
                                            onChange={(e) => handleNestedChange('attendance_preferences', 'notification_enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="attendance_notifications">Attendance Notifications</Label>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="late_threshold">Late Threshold (minutes)</Label>
                                        <Input
                                            id="late_threshold"
                                            type="number"
                                            min="0"
                                            value={formData.attendance_preferences?.late_threshold || 30}
                                            onChange={(e) => handleNestedChange('attendance_preferences', 'late_threshold', parseInt(e.target.value))}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Manual Check-in Settings</h3>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="manual_checkin_enabled"
                                            checked={formData.manual_checkin_settings?.enabled || false}
                                            onChange={(e) => handleNestedChange('manual_checkin_settings', 'enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="manual_checkin_enabled">Enable Manual Check-in</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="admin_only_checkin"
                                            checked={formData.manual_checkin_settings?.admin_only || false}
                                            onChange={(e) => handleNestedChange('manual_checkin_settings', 'admin_only', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="admin_only_checkin">Admin Only Access</Label>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="checkin_window">Check-in Time Window (minutes)</Label>
                                        <Input
                                            id="checkin_window"
                                            type="number"
                                            min="0"
                                            value={formData.manual_checkin_settings?.time_window || 120}
                                            onChange={(e) => handleNestedChange('manual_checkin_settings', 'time_window', parseInt(e.target.value))}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* System Settings Section */}
                    <Card className="p-6">
                        <div className="mb-6 flex items-center gap-2">
                            <Database className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">System Settings</h2>
                        </div>

                        <div className="space-y-6">
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Notification Preferences</h3>
                                <div className="grid gap-4 md:grid-cols-3">
                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="email_notifications"
                                            checked={formData.notification_settings?.email_enabled || false}
                                            onChange={(e) => handleNestedChange('notification_settings', 'email_enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="email_notifications">Email Notifications</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="sms_notifications"
                                            checked={formData.notification_settings?.sms_enabled || false}
                                            onChange={(e) => handleNestedChange('notification_settings', 'sms_enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="sms_notifications">SMS Notifications</Label>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="push_notifications"
                                            checked={formData.notification_settings?.push_enabled || false}
                                            onChange={(e) => handleNestedChange('notification_settings', 'push_enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="push_notifications">Push Notifications</Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Integration Settings Section */}
                    <Card className="p-6">
                        <div className="mb-6 flex items-center gap-2">
                            <Smartphone className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Integration Settings</h2>
                        </div>

                        <div className="space-y-6">
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Mobile App Configuration</h3>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="app_name">App Name</Label>
                                        <Input
                                            id="app_name"
                                            value={formData.mobile_app_config?.app_name || ''}
                                            onChange={(e) => handleNestedChange('mobile_app_config', 'app_name', e.target.value)}
                                            placeholder="Church App"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="theme_color">Theme Color</Label>
                                        <Input
                                            id="theme_color"
                                            type="color"
                                            value={formData.mobile_app_config?.theme_color || '#34B0E0'}
                                            onChange={(e) => handleNestedChange('mobile_app_config', 'theme_color', e.target.value)}
                                        />
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="app_notifications"
                                            checked={formData.mobile_app_config?.notification_enabled || false}
                                            onChange={(e) => handleNestedChange('mobile_app_config', 'notification_enabled', e.target.checked)}
                                            className="rounded border-gray-300"
                                        />
                                        <Label htmlFor="app_notifications">Enable App Notifications</Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Button type="button" variant="outline" onClick={() => router.visit('/dashboard')}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? 'Saving...' : 'Save Settings'}
                        </Button>
                    </div>
                </form>

                {/* Confirmation Dialog */}
                <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Confirm Settings Update</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <p>Are you sure you want to save these church settings? This will update your church configuration.</p>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={confirmSubmit} disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Confirm Save'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
