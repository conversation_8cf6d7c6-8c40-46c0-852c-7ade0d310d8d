import { Breadcrumbs } from '@/components/breadcrumbs';
import { ThemeToggle } from '@/components/theme-settings';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { type BreadcrumbItem as BreadcrumbItemType } from '@/types';
import { Link } from '@inertiajs/react';
import { Bell, Globe, Search, Settings } from 'lucide-react';
import { useState } from 'react';

// Mock data for notifications
const notifications = [
    {
        id: 1,
        title: 'New member registered',
        message: '<PERSON> has joined the church',
        time: '2 minutes ago',
        type: 'info',
        unread: true,
    },
    {
        id: 2,
        title: 'Service attendance updated',
        message: 'Sunday service attendance: 156 members',
        time: '1 hour ago',
        type: 'success',
        unread: true,
    },
    {
        id: 3,
        title: 'Report generated',
        message: 'Monthly financial report is ready',
        time: '3 hours ago',
        type: 'warning',
        unread: false,
    },
];

export function AppSidebarHeaderFixed({ breadcrumbs = [] }: { breadcrumbs?: BreadcrumbItemType[] }) {
    const [searchQuery, setSearchQuery] = useState('');

    const unreadCount = notifications.filter((n) => n.unread).length;

    return (
        <header className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-2 border-b border-sidebar-border/50 bg-topbar-bg px-6 shadow-sm transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4">
            {/* Left Section - Sidebar Trigger & Breadcrumbs */}
            <div className="flex flex-1 items-center gap-3">
                <SidebarTrigger className="-ml-1 text-topbar-item hover:bg-sidebar-accent/50 hover:text-topbar-item-hover" />
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            </div>

            {/* Center Section - Search */}
            <div className="hidden max-w-md flex-1 items-center gap-2 md:flex">
                <div className="relative w-full">
                    <Search className="pointer-events-none absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                        type="search"
                        placeholder="Search members, reports, settings..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="h-9 border-border/50 bg-topbar-search-bg pl-10 transition-all duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/25"
                    />
                </div>
            </div>

            {/* Right Section - Actions */}
            <div className="flex items-center gap-1">
                {/* Mobile Search */}
                <Button
                    variant="ghost"
                    size="icon"
                    className="h-9 w-9 text-topbar-item transition-colors duration-200 hover:bg-sidebar-accent/50 hover:text-topbar-item-hover md:hidden"
                >
                    <Search className="h-4 w-4" />
                    <span className="sr-only">Search</span>
                </Button>

                {/* Language Selector */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9 text-topbar-item transition-colors duration-200 hover:bg-sidebar-accent/50 hover:text-topbar-item-hover"
                        >
                            <Globe className="h-4 w-4" />
                            <span className="sr-only">Language</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>Choose Language</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="cursor-pointer">
                            <span className="mr-2">🇺🇸</span>
                            English
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer">
                            <span className="mr-2">🇪🇸</span>
                            Spanish
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer">
                            <span className="mr-2">🇫🇷</span>
                            French
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Notifications */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="relative h-9 w-9 text-topbar-item transition-colors duration-200 hover:bg-sidebar-accent/50 hover:text-topbar-item-hover"
                        >
                            <Bell className="h-4 w-4" />
                            {unreadCount > 0 && (
                                <Badge
                                    variant="destructive"
                                    className="notification-badge absolute -top-1 -right-1 flex h-5 w-5 min-w-[1.25rem] items-center justify-center rounded-full border-2 border-topbar-bg p-0 text-xs"
                                >
                                    {unreadCount > 9 ? '9+' : unreadCount}
                                </Badge>
                            )}
                            <span className="sr-only">Notifications ({unreadCount} unread)</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-80">
                        <DropdownMenuLabel className="flex items-center justify-between">
                            <span>Notifications</span>
                            {unreadCount > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                    {unreadCount} new
                                </Badge>
                            )}
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="max-h-96 overflow-y-auto">
                            {notifications.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-8 text-center">
                                    <Bell className="mb-2 h-8 w-8 text-muted-foreground/50" />
                                    <p className="text-sm text-muted-foreground">No notifications</p>
                                </div>
                            ) : (
                                notifications.map((notification) => (
                                    <DropdownMenuItem
                                        key={notification.id}
                                        className="flex cursor-pointer flex-col items-start gap-1 p-3 transition-colors duration-200 hover:bg-accent/50"
                                    >
                                        <div className="flex w-full items-center justify-between">
                                            <div className="text-sm font-medium">{notification.title}</div>
                                            {notification.unread && <div className="h-2 w-2 rounded-full bg-primary"></div>}
                                        </div>
                                        <div className="line-clamp-2 text-xs text-muted-foreground">{notification.message}</div>
                                        <div className="text-xs text-muted-foreground/70">{notification.time}</div>
                                    </DropdownMenuItem>
                                ))
                            )}
                        </div>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="w-full cursor-pointer justify-center text-primary hover:bg-accent/50">
                            <Link href="/notifications" prefetch className="w-full text-center">
                                View all notifications
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                {/* Settings - Using standard onClick instead of asChild */}
                <Link href="/settings/profile" prefetch>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9 text-topbar-item transition-colors duration-200 hover:bg-sidebar-accent/50 hover:text-topbar-item-hover"
                    >
                        <Settings className="h-4 w-4" />
                        <span className="sr-only">Settings</span>
                    </Button>
                </Link>

                {/* Theme Toggle */}
                <div className="ml-1">
                    <ThemeToggle className="h-9 w-9 text-topbar-item transition-colors duration-200 hover:bg-sidebar-accent/50 hover:text-topbar-item-hover" />
                </div>
            </div>
        </header>
    );
}
