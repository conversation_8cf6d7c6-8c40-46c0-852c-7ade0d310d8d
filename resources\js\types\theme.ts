/**
 * Velonic Theme System Types
 * Based on the comprehensive Velonic theme analysis
 */

// === VELONIC BRAND COLORS ===
export type VelonicBrandColor = 
  | 'blue'
  | 'indigo' 
  | 'purple'
  | 'pink'
  | 'red'
  | 'orange'
  | 'yellow'
  | 'green'
  | 'teal'
  | 'cyan';

// === SEMANTIC THEME COLORS ===
export type SemanticColor = 
  | 'primary'
  | 'secondary'
  | 'success'
  | 'info'
  | 'warning'
  | 'destructive'
  | 'muted'
  | 'accent';

// === EXTENDED COLOR VARIANTS ===
export type ExtendedColor = SemanticColor | 'pink' | 'purple';

// === GRAY SCALE SYSTEM ===
export type GrayScale = 
  | '50'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900'
  | '950';

// === THEME MODES ===
export type ThemeMode = 'light' | 'dark';

// === SIDEBAR THEME VARIANTS ===
export type SidebarTheme = 'light' | 'dark';

// === TOPBAR THEME VARIANTS ===
export type TopbarTheme = 'light' | 'dark';

// === LAYOUT DIMENSIONS ===
export interface LayoutDimensions {
  sidebarWidth: number;        // 240px
  sidebarWidthMd: number;      // 160px  
  sidebarWidthSm: number;      // 70px
  topbarHeight: number;        // 70px
  logoHeight: number;          // 24px
}

// === FONT WEIGHTS ===
export type FontWeight = 
  | 'light'     // 300
  | 'normal'    // 400
  | 'medium'    // 500
  | 'semibold'  // 600
  | 'bold'      // 700
  | 'black';    // 900

// === BORDER RADIUS SYSTEM ===
export type BorderRadius = 
  | 'sm'   // 4px
  | 'md'   // 6px - 2px
  | 'lg'   // 8px
  | 'xl'   // 16px
  | 'xxl'; // 32px

// === FONT SIZES ===
export type FontSize = 
  | '10'    // 0.625rem
  | '11'    // 0.6875rem
  | '12'    // 0.75rem
  | '13'    // 0.8125rem
  | '14'    // 0.875rem
  | '15'    // 0.9375rem
  | '16'    // 1rem
  | '18'    // 1.125rem
  | '20'    // 1.25rem
  | '24';   // 1.5rem

// === THEME CONFIGURATION ===
export interface ThemeConfig {
  mode: ThemeMode;
  sidebar: SidebarTheme;
  topbar: TopbarTheme;
  primaryColor: VelonicBrandColor;
}

// === THEME COLORS MAPPING ===
export interface ThemeColors {
  // Velonic Brand Colors
  blue: string;      // #4489e4
  indigo: string;    // #33b0e0
  purple: string;    // #716cb0
  pink: string;      // #f24f7c
  red: string;       // #d03f3f
  orange: string;    // #f7931e
  yellow: string;    // #f9c851
  green: string;     // #22c55e
  teal: string;      // #0891b2
  cyan: string;      // #06b6d4
  
  // Grayscale
  gray: Record<GrayScale, string>;
  
  // Semantic Colors
  primary: string;
  secondary: string;
  success: string;
  info: string;
  warning: string;
  destructive: string;
  muted: string;
  accent: string;
  
  // Layout Colors
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  border: string;
  input: string;
  ring: string;
}

// === COMPONENT THEME PROPS ===
export interface ButtonThemeProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'soft';
  color?: SemanticColor | VelonicBrandColor;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface BadgeThemeProps {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  color?: SemanticColor | VelonicBrandColor;
  size?: 'sm' | 'md' | 'lg';
}

export interface CardThemeProps {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

// === MENU ITEM THEME ===
export interface MenuItemThemeProps {
  active?: boolean;
  disabled?: boolean;
  level?: number; // For nested menu items
}

// === THEME CONTEXT ===
export interface ThemeContextType {
  config: ThemeConfig;
  colors: ThemeColors;
  dimensions: LayoutDimensions;
  updateTheme: (config: Partial<ThemeConfig>) => void;
  toggleTheme: () => void;
  setSidebarTheme: (theme: SidebarTheme) => void;
  setTopbarTheme: (theme: TopbarTheme) => void;
  setPrimaryColor: (color: VelonicBrandColor) => void;
}

// === UTILITY TYPES ===
export type ThemeColorWithOpacity = `${SemanticColor | VelonicBrandColor}/${number}`;
export type ThemeColorWithShade = `${VelonicBrandColor}-${GrayScale}`;

// === CSS CUSTOM PROPERTIES ===
export type CSSCustomProperty = 
  | `--color-${VelonicBrandColor}`
  | `--color-gray-${GrayScale}`
  | `--color-${SemanticColor}`
  | `--color-${SemanticColor}-foreground`
  | `--radius-${BorderRadius}`
  | `--font-size-${FontSize}`;

// === THEME SETTINGS CONSTANTS ===
export const VELONIC_COLORS: Record<VelonicBrandColor, string> = {
  blue: '#4489e4',
  indigo: '#33b0e0', 
  purple: '#716cb0',
  pink: '#f24f7c',
  red: '#d03f3f',
  orange: '#f7931e',
  yellow: '#f9c851',
  green: '#22c55e',
  teal: '#0891b2',
  cyan: '#06b6d4',
} as const;

export const GRAY_COLORS: Record<GrayScale, string> = {
  '50': '#f9fafb',
  '100': '#f3f4f6',
  '200': '#e5e7eb',
  '300': '#d1d5db',
  '400': '#9ca3af',
  '500': '#6b7280',
  '600': '#4b5563',
  '700': '#374151',
  '800': '#1f2937',
  '900': '#111827',
  '950': '#030712',
} as const;

export const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: 'light',
  sidebar: 'light',
  topbar: 'light',
  primaryColor: 'cyan',
} as const;

export const LAYOUT_DIMENSIONS: LayoutDimensions = {
  sidebarWidth: 240,
  sidebarWidthMd: 160,
  sidebarWidthSm: 70,
  topbarHeight: 70,
  logoHeight: 24,
} as const;

// === COLOR VARIANT ARRAYS ===
export const COLOR_VARIANTS: SemanticColor[] = [
  'primary',
  'secondary', 
  'success',
  'destructive',
  'warning',
  'info',
  'muted',
  'accent',
] as const;

export const EXTENDED_COLOR_VARIANTS: ExtendedColor[] = [
  ...COLOR_VARIANTS,
  'pink',
  'purple',
] as const;

export const BRAND_COLOR_VARIANTS: VelonicBrandColor[] = [
  'blue',
  'indigo',
  'purple', 
  'pink',
  'red',
  'orange',
  'yellow',
  'green',
  'teal',
  'cyan',
] as const;

// === THEME UTILITY FUNCTIONS ===
export function isValidThemeColor(color: string): color is SemanticColor | VelonicBrandColor {
  return [...COLOR_VARIANTS, ...BRAND_COLOR_VARIANTS].includes(color as any);
}

export function getThemeColorValue(color: SemanticColor | VelonicBrandColor): string {
  if (color in VELONIC_COLORS) {
    return VELONIC_COLORS[color as VelonicBrandColor];
  }
  return `var(--color-${color})`;
}

export function createThemeColorClass(color: SemanticColor | VelonicBrandColor, type: 'bg' | 'text' | 'border' = 'bg'): string {
  return `${type}-${color}`;
}

export function createSoftColorClass(color: SemanticColor | VelonicBrandColor): string {
  return `bg-${color}-subtle`;
}
